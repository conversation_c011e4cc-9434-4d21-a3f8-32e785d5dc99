import type { Todo } from "@/types/todo";
import { loadTodos, saveTodos } from '@/utils/storage';
import { ref, watch } from 'vue';

// 创建全局共享的响应式数据
const todos = ref<Todo[]>(loadTodos());

// 只创建一次 watch 监听器
watch(todos, () => {
  saveTodos(todos.value);
}, { deep: true });

function useTodo() {

  function addTodo(content: string) {
    if (!content.trim()) return;
    todos.value.push({
      id: Date.now(),
      content,
      completed: false
    });
  }

  function toggleTodo(id: number) {
    console.log("🚀 ~~ toggleTodo ~ id 🤖--EndLog--🤖", id);
    const todo = todos.value.find(t => t.id === id);

    console.log("🚀 ~~ toggleTodo ~~ todo completed🤖", todo?.completed);


    if (todo) {
      todo.completed = !todo.completed;
      console.log("🚀 ~~ toggleTodo ~~ todo 🤖--EndLog--🤖", todo);
    }
  }

  function deleteTodo(id: number) {

    console.log("🚀 ~~ deleteTodo ~~ id: 🤖--EndLog--🤖", id);

    todos.value = todos.value.filter(t => t.id !== id);
  }

  return {
    todos,
    addTodo,
    toggleTodo,
    deleteTodo
  };
}


export default useTodo;
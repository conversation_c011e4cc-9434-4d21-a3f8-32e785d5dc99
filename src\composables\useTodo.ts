import type { Todo } from "@/types/todo";
import { loadTodos, saveTodos } from '@/utils/storage';
import { ref, watch } from 'vue';

function useTodo() {
  const todos = ref<Todo[]>(loadTodos());

  // 每次变动后保存到本地
  watch(todos, () => {
    saveTodos(todos.value);
  }, { deep: true });

  function addTodo(content: string) {
    if (!content.trim()) return;
    todos.value.push({
      id: Date.now(),
      content,
      completed: false
    });
  }

  function toggleTodo(id: number) {
    console.log("🚀 ~~ toggleTodo ~ id 🤖--EndLog--🤖", id);
    const todo = todos.value.find(t => t.id === id);

    console.log("🚀 ~~ toggleTodo ~~ todo completed🤖", todo?.completed);

    if (todo) {
      todo.completed = !todo.completed;
      console.log("🚀 ~~ toggleTodo ~~ todo 🤖--EndLog--🤖", todo);
    }
  }

  function deleteTodo(id: number) {

    console.log("🚀 ~~ deleteTodo ~~ id: 🤖--EndLog--🤖", id);

    todos.value = todos.value.filter(t => t.id !== id);
  }

  return {
    todos,
    addTodo,
    toggleTodo,
    deleteTodo
  };
}


export default useTodo;
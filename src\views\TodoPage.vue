<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<template>
  <div>
    <div class="todo-container">
      <div class="todo-header">
        <div class="toto-header-title">待办</div>
        <div></div>
      </div>
      <div class="todo-create">
        <div :class="isShowTools ? 'tools-margin' : ''">
          <input
            placeholder="添加待办事项"
            v-model="todo"
            @keyup.enter="createTodoEnter"
            type="text"
            class="todo-input"
            @focus="handleFocusShow"
          />
        </div>
        <div class="todo-create-tools" v-show="isShowTools">
          <div class="todo-member-selector">参与人</div>
          <div class="todo-date-selector">
            <div class="todo-date-selector-item">今天</div>
            <div class="todo-date-selector-item">明天</div>
            <div class="todo-date-selector-item">其他时间</div>
          </div>
          <button @click="createTodoBtn">新建</button>
        </div>
      </div>
      <div class="todo-list-content" @click="handleClicktodos">
        <div class="todo-list-nodo" v-if="todos.values.length === 0">
          <TodoItem
            v-for="item in undonetodos"
            :key="item.id"
            :todo="item"
            @click="toggleTodo(item.id)"
          />
        </div>
        <div class="list-temp" v-show="isShowNoAllTodo || isShowDone">
          <svg
            t="1754363775331"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="5087"
            width="128"
            height="128"
          >
            <path
              d="M370.817941 216.765019h142.206045l51.327294 0.064c31.807563 0.063999 63.551126 0.127998 95.358689-0.191998a72.511003 72.511003 0 0 0 51.199296-21.375706 71.359019 71.359019 0 0 0 20.671716-51.263295c0.127998-23.29568 0-46.591359 0-69.759041C731.452983 31.167571 700.157413 0 656.574012 0H369.281962c-44.031395 0-74.94297 30.847576-75.198966 74.494976-0.127998 16.639771-0.063999 33.087545-0.063999 49.599318v16.511773c0.127998 45.759371 30.719578 76.158953 76.798944 76.158952z m-3.391953-143.99802h290.876v71.359019H367.425988V72.830999zM298.242939 651.319044h213.949058c71.039023 0 142.078046 0 213.11707-0.191997 4.479938 0.063999 9.087875-0.255996 13.567813-0.959987a36.159503 36.159503 0 0 0 29.055601-32.191557c1.47198-22.847686-14.847796-39.29546-39.423458-39.29546H296.834959a59.839177 59.839177 0 0 0-8.319886 0.639991 34.559525 34.559525 0 0 0-29.439595 25.599648 34.495526 34.495526 0 0 0 11.071847 37.439486c7.871892 6.399912 17.919754 9.599868 28.031615 8.959876zM294.594989 831.98856h378.810792c19.583731 0 39.231461 0 58.87919-0.127998a35.839507 35.839507 0 0 0 33.087545-22.335693 35.071518 35.071518 0 0 0 2.559965-13.695812c0-21.05571-15.99978-36.095504-38.719468-36.095503H295.234981a35.839507 35.839507 0 0 0-29.695592 13.695811 34.17553 34.17553 0 0 0-4.35194 38.015478 34.367527 34.367527 0 0 0 33.40754 20.479718zM481.600418 397.946528H294.850986a36.671496 36.671496 0 0 0-35.839507 21.631703 35.839507 35.839507 0 0 0 21.119709 48.447334 36.671496 36.671496 0 0 0 14.5918 2.047971 53544.415764 53544.415764 0 0 0 193.597338 0.064h96.638671a36.671496 36.671496 0 0 0 36.223502-27.519622 35.519512 35.519512 0 0 0-19.711729-41.215433 36.3515 36.3515 0 0 0-16.127778-3.327955c-34.559525-0.191997-69.11905-0.127998-103.678575-0.127998z"
              p-id="5088"
              fill="#707070"
            ></path>
            <path
              d="M64.00616 169.661667v750.389682a102.078596 102.078596 0 0 0 30.079586 73.726987A103.102582 103.102582 0 0 0 168.00473 1023.98592h687.99054c14.847796 0.255996 29.567593-2.94396 42.94341-9.215873 40.767439-19.903726 60.991161-52.991271 60.991161-97.918654V171.517642l-0.063999-7.807893c-1.663977-45.951368-33.855534-86.52681-79.230911-95.806683-15.231791-3.199956-31.359569-3.199956-47.295349-3.135957h-11.519842a34.303528 34.303528 0 0 0-31.935561 21.183709 33.791535 33.791535 0 0 0 8.127888 37.311487 34.751522 34.751522 0 0 0 24.575662 9.663867h13.759811l20.479719 0.063999a34.047532 34.047532 0 0 1 27.199626 13.631813 40.959437 40.959437 0 0 1 7.167901 26.17564l-0.063999 744.69376c-0.127998 23.807673-14.271804 38.079476-37.503484 38.079477H169.924704c-22.52769 0-37.11949-14.463801-37.11949-36.927492V169.597668c0-22.079696 14.463801-36.479498 36.479498-36.799494h12.863823c7.167901 0.127998 14.271804 0.191997 21.439706-0.063999a34.17553 34.17553 0 0 0 32.447554-32.639551c0.831989-17.279762-11.64784-33.791535-28.671606-35.007519a265.404351 265.404351 0 0 0-54.143256 0.639991C99.525672 73.342992 64.00616 115.582411 64.00616 169.597668z"
              p-id="5089"
              fill="#707070"
            ></path>
          </svg>
        </div>
        <div class="todo-list-show" @click="toggle" v-show="donetodos.length > 0">
          <svg
            class="arrow-icon"
            :class="{ rotated: isOpen }"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="2321"
            width="32"
            height="32"
          >
            <path
              d="M256 341.333333c12.8 0 21.333333 4.266667 29.866667 12.8l226.133333 226.133334 226.133333-226.133334c17.066667-17.066667 42.666667-17.066667 59.733334 0s17.066667 42.666667 0 59.733334l-256 256c-17.066667 17.066667-42.666667 17.066667-59.733334 0l-256-256c-17.066667-17.066667-17.066667-42.666667 0-59.733334 8.533333-8.533333 17.066667-12.8 29.866667-12.8z"
              p-id="2322"
              fill="#8a8a8a"
            ></path></svg
          ><span>{{ isShowDone ? '隐藏已完成' : '显示已完成' }}</span>
        </div>
        <div class="no-all-todo" v-show="isShowNoAllTodo">
          <p class="no-todo">暂无待办</p>
          <p class="undone-tips">在消息中创建的待办，将会显示在这里</p>
        </div>
        <div class="todo-show-done" v-show="isShowDone">
          <TodoItem v-for="item in donetodos" :key="item.id" :todo="item" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import TodoItem from '@/components/TodoItem.vue'
import useTodo from '@/composables/useTodo'
import type { Todo } from '@/types/todo'
import { computed, provide, ref } from 'vue'
//tools工具标志
const isShowTools = ref<boolean>(false)

//箭头翻转
const isOpen = ref(false)

//已完成容器显示
const isShowDone = ref<boolean>(false)

//从本地加载数据
const { todos, addTodo, toggleTodo, deleteTodo } = useTodo()
console.log('初始todos数据', todos.value)

// 提供给子组件使用
provide('todoActions', {
  toggleTodo,
  deleteTodo,
})
//input双向绑定
const todo = ref<string>('')

//input被focus才显示tools工具，并添加动态class
const handleFocusShow = () => {
  isShowTools.value = true
}

//点击todos的显示区域后，隐藏tools工具
const handleClicktodos = (event: MouseEvent) => {
  console.log('div 被点击了', event)
  isShowTools.value = false
}

//键盘enter事件
const createTodoEnter = () => {
  console.log('enter')
  addTodo(todo.value)
  //清空输入框
  todo.value = ''
  console.log('点击了键盘，查看todos数据', todos.value)
}
//点击按钮添加todo项
const createTodoBtn = () => {
  addTodo(todo.value)
  //清空输入框
  todo.value = ''
  console.log('点击了按钮，查看todos数据', todos.value)
}

//计算属性，根据todo的completed属性，过滤出未完成的todo项
const undonetodos = computed(() => {
  return todos.value.filter((todo: Todo) => !todo.completed)
})

// 根据todo的completed属性，过滤出已完成的todo项
const donetodos = computed(() => {
  return todos.value.filter((todo: Todo) => todo.completed)
})

// 根据已完成todo项来判断是否显示已完成项的点击按钮

//根据是否有数据来判断是否显示no-all-todo
const isShowNoAllTodo = computed(() => {
  return todos.value.length === 0
})

//箭头翻转的同时要显示和隐藏已完成的项
const toggle = () => {
  isOpen.value = !isOpen.value
  isShowDone.value = !isShowDone.value
}
</script>

<style lang="css" scoped>
* {
  outline: none;
}
input {
  all: unset;
}
/*在触发了focus后才添加属性 */
.tools-margin {
  margin-bottom: 10px;
}
.todo-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  width: 100%;
  font-size: 15px;
  background-color: #fff;
  gap: 10px;
}
.todo-header {
  display: flex;
  justify-content: space-between;
  height: 20px;
}
.toto-header-title {
  font-weight: bold;
  padding-left: 8px;
}
.todo-create {
  display: flex;
  gap: 10px;
  flex-direction: column;
  border: 1px solid #2572e6;
  border-radius: 6px;
  height: auto;
  padding: 6px 12px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.todo-create-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.todo-date-selector {
  display: flex;
  justify-content: space-between;
  gap: 5px;
}
.todo-member-selector,
.todo-date-selector-item {
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 4px 12px;
  cursor: pointer;
}
.todo-date-selector-item:hover {
  background-color: #eee;
}
.todo-create-tools button {
  border: none;
  border-radius: 6px;
  padding: 4px 15px;
  cursor: pointer;
  background-color: #007fff;
  color: #fff;
}
.todo-list-content {
  display: flex;
  overflow-y: overlay;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  padding: 0 10px;
}
.no-all-todo {
  text-align: center;
}
.list-temp {
  align-self: center;
}
.list-temp svg {
  width: 90px;
  height: 90px;
}
.no-todo {
  color: rgba(13, 13, 13, 0.9);
  margin: 0;
  font-weight: 600;
  margin-top: 20px;
}
.undone-tips {
  color: rgba(13, 13, 13, 0.6);
  margin: 4px 0 0;
}
.todo-list-nodo {
  display: flex;
  flex-direction: column;
}
.todo-list-show {
  display: flex;
  justify-content: center;
  align-items: center;
}

/*箭头样式 */
.arrow-icon {
  transition: transform 0.1s ease;
  transform-origin: center;
  width: 25px;
  height: 25px;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}
</style>

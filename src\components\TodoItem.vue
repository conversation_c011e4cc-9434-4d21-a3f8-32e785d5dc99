<template>
  <div class="todo-item" @mousedown="handleRightClick">
    <div class="chose-box">
      <label>
        <input type="checkbox" :checked="todo.completed" @click="toggleTodo(todo.id)" />
      </label>
    </div>
    <div class="todo-content">{{ todo.content }}</div>
    <div class="todo-item-modal" v-show="isShowModal">
      <button @click="deleteTodo(todo.id)">删除</button>
    </div>
  </div>
</template>
<script setup lang="ts">
import useTodo from '@/composables/useTodo'
import { type Todo } from '@/types/todo'
import { onMounted, onUnmounted, ref } from 'vue'

const { toggleTodo, deleteTodo } = useTodo()

//控制模态框的显示
const isShowModal = ref<boolean>(false)
defineProps<{
  todo: Todo
}>()

//右击鼠标删除
const handleRightClick = (e: MouseEvent) => {
  if (e.button === 2) {
    e.preventDefault() // 阻止默认右键菜单
    console.log('右击')
    isShowModal.value = true
  }
}

//模态框关闭监听
const handleClickOutside = () => {
  isShowModal.value = false
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="css" scoped>
.todo-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0;
}

.todo-item:hover {
  background-color: rgb(241, 241, 241);
}
.chose-box {
  flex: 0 0 13px;
}
.todo-item ::before {
  content: '';
  background-color: rgb(241, 241, 241);

  height: 1px;
  position: absolute;
  display: block;
  position: absolute;
  top: 39px;
  left: 12px;
  right: 12px;
}
.todo-item-modal {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
</style>

# vue3-practice1

## 项目目录结构

src/
├── components/        # 通用组件（如 TodoItem.vue）
├── views/             # 页面级组件（如 TodoPage.vue）
├── composables/       # 封装的逻辑函数（如 useTodo.ts）
├── types/             # 类型定义（如 todo.d.ts）
├── utils/             # 工具函数（如 storage.ts）
├── stores/            # 状态管理
├── App.vue
├── main.ts

## 健壮性考虑

### 1. **类型安全**

* 所有数据结构、props、emit 事件都要有明确的类型。
* 使用组合式函数封装逻辑，避免组件臃肿。

### 2. **容错处理**

* JSON 解析加 `try-catch`，防止 localStorage 数据损坏报错。
* 添加任务时进行空字符串判断。

### 3. **性能**

* 使用 `watch` 深度监听列表变动，避免重复手动保存。

### 4. **模块解耦**

* 把业务逻辑拆到 `composables/`，localStorage 封装在 `utils/`。
* 便于未来切换 Vuex、Pinia 或持久化方案。

### 5. **上线准备**

* 使用 ESLint + Prettier 保证代码质量。
* 使用 Vite 进行构建，优化打包体积。
* 提前测试本地缓存（localStorage）是否能正常持久化。

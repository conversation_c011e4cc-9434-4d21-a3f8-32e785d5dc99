{
  "files": [],
  "references": [
    {
      "path": "./tsconfig.node.json"
    },
    {
      "path": "./tsconfig.app.json"
    }
  ],
  "compilerOptions": {
    "resolveJsonModule": true, // 允许导入 JSON 文件
    "esModuleInterop": true, // 兼容 commonjs
    "forceConsistentCasingInFileNames": true, // 文件名大小写检查
    "skipLibCheck": true, // 跳过依赖库的类型检查（提升速度）
    "baseUrl": ".", // 允许使用绝对路径
    "paths": {
      "@/*": ["src/*"] // 配置别名 @
    }
  }
}
